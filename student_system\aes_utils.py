"""
AES Encryption/Decryption Utilities for Student Information System

This module provides AES encryption and decryption functions using the cryptography library.
It handles key generation, encryption, and decryption of sensitive data.
"""

import os
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import hashlib


class AESCipher:
    """
    AES encryption/decryption class using CBC mode with PKCS7 padding
    """
    
    def __init__(self, key=None):
        """
        Initialize AES cipher with a key
        
        Args:
            key (bytes, optional): 32-byte encryption key. If None, generates a new key.
        """
        if key is None:
            self.key = self.generate_key()
        else:
            self.key = key
    
    @staticmethod
    def generate_key():
        """
        Generate a random 256-bit (32-byte) AES key
        
        Returns:
            bytes: 32-byte encryption key
        """
        return os.urandom(32)
    
    @staticmethod
    def derive_key_from_password(password):
        """
        Derive a 256-bit key from a password using SHA-256
        
        Args:
            password (str): Password to derive key from
            
        Returns:
            bytes: 32-byte encryption key
        """
        return hashlib.sha256(password.encode()).digest()
    
    def encrypt(self, plaintext):
        """
        Encrypt plaintext using AES-256-CBC
        
        Args:
            plaintext (str): Text to encrypt
            
        Returns:
            str: Base64 encoded encrypted data (IV + ciphertext)
        """
        # Convert string to bytes
        data = plaintext.encode('utf-8')
        
        # Generate random IV (Initialization Vector)
        iv = os.urandom(16)
        
        # Create cipher
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # Add PKCS7 padding
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data)
        padded_data += padder.finalize()
        
        # Encrypt the data
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        
        # Combine IV and ciphertext, then encode as base64
        encrypted_data = iv + ciphertext
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def decrypt(self, encrypted_data):
        """
        Decrypt AES-256-CBC encrypted data
        
        Args:
            encrypted_data (str): Base64 encoded encrypted data
            
        Returns:
            str: Decrypted plaintext
            
        Raises:
            ValueError: If decryption fails
        """
        try:
            # Decode from base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # Extract IV (first 16 bytes) and ciphertext
            iv = encrypted_bytes[:16]
            ciphertext = encrypted_bytes[16:]
            
            # Create cipher
            cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            # Decrypt the data
            padded_data = decryptor.update(ciphertext) + decryptor.finalize()
            
            # Remove PKCS7 padding
            unpadder = padding.PKCS7(128).unpadder()
            data = unpadder.update(padded_data)
            data += unpadder.finalize()
            
            # Convert bytes back to string
            return data.decode('utf-8')
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")


def save_key_to_file(key, filename):
    """
    Save encryption key to a file (base64 encoded)
    
    Args:
        key (bytes): Encryption key
        filename (str): File path to save key
    """
    with open(filename, 'wb') as f:
        f.write(base64.b64encode(key))


def load_key_from_file(filename):
    """
    Load encryption key from a file
    
    Args:
        filename (str): File path to load key from
        
    Returns:
        bytes: Encryption key
        
    Raises:
        FileNotFoundError: If key file doesn't exist
    """
    try:
        with open(filename, 'rb') as f:
            return base64.b64decode(f.read())
    except FileNotFoundError:
        raise FileNotFoundError(f"Key file '{filename}' not found")


def encrypt_file_data(data, password):
    """
    Encrypt data using a password-derived key
    
    Args:
        data (str): Data to encrypt
        password (str): Password to derive encryption key from
        
    Returns:
        str: Encrypted data (base64 encoded)
    """
    key = AESCipher.derive_key_from_password(password)
    cipher = AESCipher(key)
    return cipher.encrypt(data)


def decrypt_file_data(encrypted_data, password):
    """
    Decrypt data using a password-derived key
    
    Args:
        encrypted_data (str): Encrypted data (base64 encoded)
        password (str): Password to derive decryption key from
        
    Returns:
        str: Decrypted data
        
    Raises:
        ValueError: If decryption fails
    """
    key = AESCipher.derive_key_from_password(password)
    cipher = AESCipher(key)
    return cipher.decrypt(encrypted_data)


# Test function for the AES utilities
def test_aes_encryption():
    """
    Test function to verify AES encryption/decryption works correctly
    """
    print("Testing AES encryption/decryption...")
    
    # Test with generated key
    cipher = AESCipher()
    test_data = "This is a test message for AES encryption!"
    
    encrypted = cipher.encrypt(test_data)
    decrypted = cipher.decrypt(encrypted)
    
    print(f"Original: {test_data}")
    print(f"Encrypted: {encrypted}")
    print(f"Decrypted: {decrypted}")
    print(f"Test passed: {test_data == decrypted}")
    
    # Test with password-derived key
    password = "test_password_123"
    encrypted_with_password = encrypt_file_data(test_data, password)
    decrypted_with_password = decrypt_file_data(encrypted_with_password, password)
    
    print(f"\nPassword-based encryption test:")
    print(f"Test passed: {test_data == decrypted_with_password}")


if __name__ == "__main__":
    test_aes_encryption()
