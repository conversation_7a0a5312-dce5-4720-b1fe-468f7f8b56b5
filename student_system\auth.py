"""
Authentication System for Student Information System

This module handles user registration, login, and password management with AES encryption.
User credentials are stored in an encrypted file for security.
"""

import json
import os
from aes_utils import AESCipher, encrypt_file_data, decrypt_file_data


class AuthenticationSystem:
    """
    Handles user authentication with encrypted password storage
    """
    
    def __init__(self, users_file="users.dat", master_password="SIS_MASTER_2024"):
        """
        Initialize authentication system
        
        Args:
            users_file (str): File to store encrypted user data
            master_password (str): Master password for encrypting user data
        """
        self.users_file = users_file
        self.master_password = master_password
        self.users = self.load_users()
    
    def load_users(self):
        """
        Load users from encrypted file
        
        Returns:
            dict: Dictionary of users {username: encrypted_password}
        """
        if not os.path.exists(self.users_file):
            return {}
        
        try:
            with open(self.users_file, 'r') as f:
                encrypted_data = f.read().strip()
            
            if not encrypted_data:
                return {}
            
            # Decrypt user data
            decrypted_data = decrypt_file_data(encrypted_data, self.master_password)
            return json.loads(decrypted_data)
            
        except Exception as e:
            print(f"Error loading users: {e}")
            return {}
    
    def save_users(self):
        """
        Save users to encrypted file
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert users dict to JSON string
            users_json = json.dumps(self.users, indent=2)
            
            # Encrypt the data
            encrypted_data = encrypt_file_data(users_json, self.master_password)
            
            # Save to file
            with open(self.users_file, 'w') as f:
                f.write(encrypted_data)
            
            return True
            
        except Exception as e:
            print(f"Error saving users: {e}")
            return False
    
    def hash_password(self, password):
        """
        Hash password using AES encryption
        
        Args:
            password (str): Plain text password
            
        Returns:
            str: Encrypted password
        """
        # Use a fixed salt for consistency (in production, use random salt per user)
        salt = "SIS_SALT_2024"
        salted_password = password + salt
        
        # Create cipher with password-derived key
        cipher = AESCipher(AESCipher.derive_key_from_password(self.master_password))
        return cipher.encrypt(salted_password)
    
    def verify_password(self, password, encrypted_password):
        """
        Verify password against encrypted version
        
        Args:
            password (str): Plain text password to verify
            encrypted_password (str): Encrypted password to compare against
            
        Returns:
            bool: True if password matches, False otherwise
        """
        try:
            # Hash the provided password
            hashed_input = self.hash_password(password)
            return hashed_input == encrypted_password
        except Exception:
            return False
    
    def register_user(self, username, password):
        """
        Register a new user
        
        Args:
            username (str): Username
            password (str): Password
            
        Returns:
            tuple: (success: bool, message: str)
        """
        # Validate input
        if not username or not password:
            return False, "Username and password cannot be empty"
        
        if len(username) < 3:
            return False, "Username must be at least 3 characters long"
        
        if len(password) < 6:
            return False, "Password must be at least 6 characters long"
        
        # Check if user already exists
        if username in self.users:
            return False, "Username already exists"
        
        try:
            # Hash password and store user
            encrypted_password = self.hash_password(password)
            self.users[username] = encrypted_password
            
            # Save to file
            if self.save_users():
                return True, "User registered successfully"
            else:
                # Remove user if save failed
                del self.users[username]
                return False, "Failed to save user data"
                
        except Exception as e:
            return False, f"Registration failed: {str(e)}"
    
    def login_user(self, username, password):
        """
        Authenticate user login
        
        Args:
            username (str): Username
            password (str): Password
            
        Returns:
            tuple: (success: bool, message: str)
        """
        # Validate input
        if not username or not password:
            return False, "Username and password cannot be empty"
        
        # Check if user exists
        if username not in self.users:
            return False, "Invalid username or password"
        
        # Verify password
        if self.verify_password(password, self.users[username]):
            return True, "Login successful"
        else:
            return False, "Invalid username or password"
    
    def change_password(self, username, old_password, new_password):
        """
        Change user password
        
        Args:
            username (str): Username
            old_password (str): Current password
            new_password (str): New password
            
        Returns:
            tuple: (success: bool, message: str)
        """
        # Verify current password
        login_success, _ = self.login_user(username, old_password)
        if not login_success:
            return False, "Current password is incorrect"
        
        # Validate new password
        if len(new_password) < 6:
            return False, "New password must be at least 6 characters long"
        
        try:
            # Update password
            encrypted_password = self.hash_password(new_password)
            self.users[username] = encrypted_password
            
            # Save to file
            if self.save_users():
                return True, "Password changed successfully"
            else:
                return False, "Failed to save password change"
                
        except Exception as e:
            return False, f"Password change failed: {str(e)}"
    
    def list_users(self):
        """
        Get list of registered usernames (for admin purposes)
        
        Returns:
            list: List of usernames
        """
        return list(self.users.keys())
    
    def user_exists(self, username):
        """
        Check if a user exists
        
        Args:
            username (str): Username to check
            
        Returns:
            bool: True if user exists, False otherwise
        """
        return username in self.users


def interactive_registration(auth_system):
    """
    Interactive user registration interface
    
    Args:
        auth_system (AuthenticationSystem): Authentication system instance
        
    Returns:
        bool: True if registration successful, False otherwise
    """
    print("\n=== User Registration ===")
    
    username = input("Enter username (min 3 characters): ").strip()
    password = input("Enter password (min 6 characters): ").strip()
    confirm_password = input("Confirm password: ").strip()
    
    if password != confirm_password:
        print("Error: Passwords do not match!")
        return False
    
    success, message = auth_system.register_user(username, password)
    print(f"\n{message}")
    return success


def interactive_login(auth_system):
    """
    Interactive user login interface
    
    Args:
        auth_system (AuthenticationSystem): Authentication system instance
        
    Returns:
        tuple: (success: bool, username: str or None)
    """
    print("\n=== User Login ===")
    
    username = input("Enter username: ").strip()
    password = input("Enter password: ").strip()
    
    success, message = auth_system.login_user(username, password)
    print(f"\n{message}")
    
    if success:
        return True, username
    else:
        return False, None


# Test function for authentication system
def test_auth_system():
    """
    Test function to verify authentication system works correctly
    """
    print("Testing Authentication System...")
    
    # Create test auth system
    auth = AuthenticationSystem("test_users.dat")
    
    # Test registration
    success, msg = auth.register_user("testuser", "testpass123")
    print(f"Registration test: {success} - {msg}")
    
    # Test login
    success, msg = auth.login_user("testuser", "testpass123")
    print(f"Login test: {success} - {msg}")
    
    # Test wrong password
    success, msg = auth.login_user("testuser", "wrongpass")
    print(f"Wrong password test: {success} - {msg}")
    
    # Clean up test file
    if os.path.exists("test_users.dat"):
        os.remove("test_users.dat")


if __name__ == "__main__":
    test_auth_system()
