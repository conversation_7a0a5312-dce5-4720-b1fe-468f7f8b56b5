"""
Student Information System - Main Application

This is the main entry point for the Student Information System.
It provides a console-based interface for user authentication and student management.
"""

import os
import sys
from auth import AuthenticationSystem, interactive_registration, interactive_login
from student import StudentManager


class StudentInformationSystem:
    """
    Main application class for the Student Information System
    """
    
    def __init__(self):
        """
        Initialize the Student Information System
        """
        self.auth_system = AuthenticationSystem()
        self.student_manager = StudentManager()
        self.current_user = None
        self.running = True
    
    def clear_screen(self):
        """
        Clear the console screen
        """
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def display_header(self):
        """
        Display application header
        """
        print("=" * 60)
        print("           STUDENT INFORMATION SYSTEM")
        print("=" * 60)
        if self.current_user:
            print(f"Logged in as: {self.current_user}")
            print("-" * 60)
    
    def display_main_menu(self):
        """
        Display main menu options
        """
        print("\nMain Menu:")
        print("1. Login")
        print("2. Register New User")
        print("3. Exit")
        print("-" * 30)
    
    def display_student_menu(self):
        """
        Display student management menu
        """
        print("\nStudent Management Menu:")
        print("1. Add Student")
        print("2. View All Students")
        print("3. Search Student")
        print("4. Update Student")
        print("5. Delete Student")
        print("6. View Statistics")
        print("7. Logout")
        print("-" * 30)
    
    def get_user_choice(self, max_choice):
        """
        Get user menu choice with validation
        
        Args:
            max_choice (int): Maximum valid choice number
            
        Returns:
            int: User's choice
        """
        while True:
            try:
                choice = int(input(f"Enter your choice (1-{max_choice}): "))
                if 1 <= choice <= max_choice:
                    return choice
                else:
                    print(f"Please enter a number between 1 and {max_choice}")
            except ValueError:
                print("Please enter a valid number")
    
    def handle_login(self):
        """
        Handle user login process
        """
        success, username = interactive_login(self.auth_system)
        if success:
            self.current_user = username
            print(f"\nWelcome, {username}!")
            input("Press Enter to continue...")
        else:
            input("Press Enter to continue...")
    
    def handle_registration(self):
        """
        Handle user registration process
        """
        success = interactive_registration(self.auth_system)
        if success:
            print("You can now login with your new account.")
        input("Press Enter to continue...")
    
    def add_student_interactive(self):
        """
        Interactive student addition
        """
        print("\n=== Add New Student ===")
        
        name = input("Enter student name: ").strip()
        student_id = input("Enter student ID: ").strip()
        grade = input("Enter student grade: ").strip()
        email = input("Enter email (optional): ").strip()
        phone = input("Enter phone (optional): ").strip()
        address = input("Enter address (optional): ").strip()
        
        success, message = self.student_manager.add_student(
            name, student_id, grade, email, phone, address
        )
        
        print(f"\n{message}")
        input("Press Enter to continue...")
    
    def view_all_students(self):
        """
        Display all students
        """
        print("\n=== All Students ===")
        
        students = self.student_manager.get_all_students()
        
        if not students:
            print("No students found.")
        else:
            print(f"Total students: {len(students)}")
            print("-" * 80)
            
            for student in sorted(students, key=lambda s: s.name):
                print(f"ID: {student.student_id:<10} | Name: {student.name:<25} | Grade: {student.grade}")
        
        input("\nPress Enter to continue...")
    
    def search_student_interactive(self):
        """
        Interactive student search
        """
        print("\n=== Search Student ===")
        print("Search by:")
        print("1. Name")
        print("2. Student ID")
        print("3. Grade")
        
        search_choice = self.get_user_choice(3)
        search_types = {1: "name", 2: "id", 3: "grade"}
        search_by = search_types[search_choice]
        
        query = input(f"Enter search term for {search_by}: ").strip()
        
        if not query:
            print("Search term cannot be empty.")
            input("Press Enter to continue...")
            return
        
        results = self.student_manager.search_students(query, search_by)
        
        if not results:
            print("No students found matching your search.")
        else:
            print(f"\nFound {len(results)} student(s):")
            print("-" * 80)
            
            for i, student in enumerate(results, 1):
                print(f"\n{i}. {student}")
        
        input("Press Enter to continue...")
    
    def update_student_interactive(self):
        """
        Interactive student update
        """
        print("\n=== Update Student ===")
        
        student_id = input("Enter student ID to update: ").strip()
        student = self.student_manager.get_student(student_id)
        
        if not student:
            print(f"Student with ID '{student_id}' not found.")
            input("Press Enter to continue...")
            return
        
        print(f"\nCurrent information for {student.name}:")
        print(student)
        
        print("\nEnter new information (press Enter to keep current value):")
        
        new_name = input(f"Name [{student.name}]: ").strip()
        new_grade = input(f"Grade [{student.grade}]: ").strip()
        new_email = input(f"Email [{student.email}]: ").strip()
        new_phone = input(f"Phone [{student.phone}]: ").strip()
        new_address = input(f"Address [{student.address}]: ").strip()
        
        # Prepare update data
        update_data = {}
        if new_name:
            update_data['name'] = new_name
        if new_grade:
            update_data['grade'] = new_grade
        if new_email:
            update_data['email'] = new_email
        if new_phone:
            update_data['phone'] = new_phone
        if new_address:
            update_data['address'] = new_address
        
        if update_data:
            success, message = self.student_manager.update_student(student_id, **update_data)
            print(f"\n{message}")
        else:
            print("\nNo changes made.")
        
        input("Press Enter to continue...")
    
    def delete_student_interactive(self):
        """
        Interactive student deletion
        """
        print("\n=== Delete Student ===")
        
        student_id = input("Enter student ID to delete: ").strip()
        student = self.student_manager.get_student(student_id)
        
        if not student:
            print(f"Student with ID '{student_id}' not found.")
            input("Press Enter to continue...")
            return
        
        print(f"\nStudent to delete:")
        print(student)
        
        confirm = input("Are you sure you want to delete this student? (yes/no): ").strip().lower()
        
        if confirm in ['yes', 'y']:
            success, message = self.student_manager.delete_student(student_id)
            print(f"\n{message}")
        else:
            print("\nDeletion cancelled.")
        
        input("Press Enter to continue...")
    
    def view_statistics(self):
        """
        Display student statistics
        """
        print("\n=== Student Statistics ===")
        
        stats = self.student_manager.get_statistics()
        
        print(f"Total Students: {stats['total_students']}")
        
        if stats['grades_distribution']:
            print("\nGrade Distribution:")
            for grade, count in sorted(stats['grades_distribution'].items()):
                print(f"  {grade}: {count} students")
        
        input("\nPress Enter to continue...")
    
    def run_student_management(self):
        """
        Run student management interface
        """
        while self.current_user:
            self.clear_screen()
            self.display_header()
            self.display_student_menu()
            
            choice = self.get_user_choice(7)
            
            if choice == 1:
                self.add_student_interactive()
            elif choice == 2:
                self.view_all_students()
            elif choice == 3:
                self.search_student_interactive()
            elif choice == 4:
                self.update_student_interactive()
            elif choice == 5:
                self.delete_student_interactive()
            elif choice == 6:
                self.view_statistics()
            elif choice == 7:
                self.current_user = None
                print("\nLogged out successfully!")
                input("Press Enter to continue...")
    
    def run(self):
        """
        Main application loop
        """
        while self.running:
            if self.current_user:
                self.run_student_management()
            else:
                self.clear_screen()
                self.display_header()
                self.display_main_menu()
                
                choice = self.get_user_choice(3)
                
                if choice == 1:
                    self.handle_login()
                elif choice == 2:
                    self.handle_registration()
                elif choice == 3:
                    print("\nThank you for using Student Information System!")
                    self.running = False


def main():
    """
    Main function to start the application
    """
    try:
        app = StudentInformationSystem()
        app.run()
    except KeyboardInterrupt:
        print("\n\nApplication interrupted by user.")
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        print("Please contact system administrator.")
    finally:
        print("Goodbye!")


if __name__ == "__main__":
    main()
