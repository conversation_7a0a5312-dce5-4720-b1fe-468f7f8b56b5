# Student Information System

A secure Python-based Student Information System with AES encryption for data protection.

## Features

### 🔐 User Authentication System
- **Secure Registration**: Create new user accounts with encrypted password storage
- **User Login**: Authenticate users with encrypted credentials
- **Password Security**: All passwords are encrypted using AES encryption
- **Session Management**: Secure user sessions with logout functionality

### 👨‍🎓 Student Record Management
- **Add Students**: Register new students with comprehensive information
- **View Students**: Display all registered students in an organized format
- **Search Students**: Find students by name, ID, or grade
- **Update Records**: Modify existing student information
- **Delete Records**: Remove student records with confirmation
- **Statistics**: View student distribution and statistics

### 🔒 Data Security
- **AES Encryption**: All sensitive data is encrypted using AES-256-CBC
- **Encrypted Storage**: Student records and user credentials stored in encrypted files
- **Secure File Handling**: Automatic encryption/decryption for data persistence
- **Password Hashing**: User passwords are hashed and encrypted before storage

### 💻 User Interface
- **Console-Based**: Simple text-based menu system
- **Interactive Menus**: Easy navigation through numbered options
- **Input Validation**: Comprehensive input validation and error handling
- **Clear Display**: Well-formatted output for easy reading

## Project Structure

```
student_system/
├── main.py          # Main application entry point and menu system
├── auth.py          # User authentication and registration system
├── aes_utils.py     # AES encryption/decryption utilities
├── student.py       # Student record management and operations
├── requirements.txt # Python dependencies
├── README.md        # This file
├── users.dat        # Encrypted user credentials (created automatically)
└── students.dat     # Encrypted student records (created automatically)
```

## Installation

1. **Clone or download** the project files to your local machine

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Usage

### First Time Setup

1. **Run the application**: `python main.py`
2. **Register a new user**: Choose option 2 from the main menu
3. **Create your account**: Enter username (min 3 chars) and password (min 6 chars)
4. **Login**: Use option 1 with your new credentials

### Student Management

Once logged in, you can:

1. **Add Student**: Enter student details (Name, ID, Grade, Email, Phone, Address)
2. **View All Students**: See a list of all registered students
3. **Search Student**: Find students by name, ID, or grade
4. **Update Student**: Modify existing student information
5. **Delete Student**: Remove a student record (with confirmation)
6. **View Statistics**: See total students and grade distribution
7. **Logout**: Return to main menu

### Data Files

The system creates two encrypted data files:
- `users.dat`: Stores encrypted user credentials
- `students.dat`: Stores encrypted student records

**Important**: These files contain encrypted data and should not be manually edited.

## Security Features

### Encryption Details
- **Algorithm**: AES-256-CBC (Advanced Encryption Standard)
- **Key Derivation**: SHA-256 for password-based key generation
- **Padding**: PKCS7 padding for block cipher compatibility
- **IV**: Random initialization vector for each encryption operation

### Password Security
- Minimum password length: 6 characters
- Passwords are salted and encrypted before storage
- No plain text passwords are stored anywhere in the system

### Data Protection
- All student records are encrypted before saving to disk
- User credentials are encrypted using a master password
- Automatic encryption/decryption during file operations

## Technical Requirements

- **Python**: 3.7 or higher
- **Dependencies**: cryptography library (see requirements.txt)
- **Operating System**: Windows, macOS, or Linux

## Error Handling

The system includes comprehensive error handling for:
- Invalid user input
- File I/O operations
- Encryption/decryption failures
- Database corruption
- Network interruptions

## Troubleshooting

### Common Issues

1. **"Module not found" error**:
   - Install dependencies: `pip install -r requirements.txt`

2. **"Decryption failed" error**:
   - Data file may be corrupted
   - Delete `.dat` files to reset (will lose all data)

3. **"Permission denied" error**:
   - Ensure write permissions in the application directory

### Data Recovery

If data files become corrupted:
1. Backup existing `.dat` files
2. Delete corrupted files
3. Restart application (will create new empty files)
4. Re-register users and re-enter student data

## Development

### Code Organization

- **main.py**: Application entry point and user interface
- **auth.py**: Authentication system with encrypted storage
- **student.py**: Student data model and management operations
- **aes_utils.py**: Encryption utilities and helper functions

### Testing

Each module includes test functions:
```bash
python aes_utils.py    # Test encryption utilities
python auth.py         # Test authentication system
python student.py      # Test student management
```

## License

This project is provided as-is for educational purposes.

## Support

For issues or questions, please review the code comments and error messages for guidance.
