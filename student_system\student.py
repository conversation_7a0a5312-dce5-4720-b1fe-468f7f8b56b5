"""
Student Record Management System

This module handles student record operations including adding, viewing, searching,
and managing student data with AES encryption for data security.
"""

import json
import os
from datetime import datetime
from aes_utils import encrypt_file_data, decrypt_file_data


class Student:
    """
    Student data model
    """
    
    def __init__(self, name, student_id, grade, email="", phone="", address=""):
        """
        Initialize student object
        
        Args:
            name (str): Student's full name
            student_id (str): Unique student ID
            grade (str): Student's grade/class
            email (str, optional): Student's email address
            phone (str, optional): Student's phone number
            address (str, optional): Student's address
        """
        self.name = name
        self.student_id = student_id
        self.grade = grade
        self.email = email
        self.phone = phone
        self.address = address
        self.created_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.last_modified = self.created_date
    
    def to_dict(self):
        """
        Convert student object to dictionary
        
        Returns:
            dict: Student data as dictionary
        """
        return {
            'name': self.name,
            'student_id': self.student_id,
            'grade': self.grade,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'created_date': self.created_date,
            'last_modified': self.last_modified
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        Create student object from dictionary
        
        Args:
            data (dict): Student data dictionary
            
        Returns:
            Student: Student object
        """
        student = cls(
            data['name'],
            data['student_id'],
            data['grade'],
            data.get('email', ''),
            data.get('phone', ''),
            data.get('address', '')
        )
        student.created_date = data.get('created_date', student.created_date)
        student.last_modified = data.get('last_modified', student.last_modified)
        return student
    
    def update(self, name=None, grade=None, email=None, phone=None, address=None):
        """
        Update student information
        
        Args:
            name (str, optional): New name
            grade (str, optional): New grade
            email (str, optional): New email
            phone (str, optional): New phone
            address (str, optional): New address
        """
        if name is not None:
            self.name = name
        if grade is not None:
            self.grade = grade
        if email is not None:
            self.email = email
        if phone is not None:
            self.phone = phone
        if address is not None:
            self.address = address
        
        self.last_modified = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def __str__(self):
        """
        String representation of student
        
        Returns:
            str: Formatted student information
        """
        return f"""
Student Information:
  Name: {self.name}
  ID: {self.student_id}
  Grade: {self.grade}
  Email: {self.email or 'Not provided'}
  Phone: {self.phone or 'Not provided'}
  Address: {self.address or 'Not provided'}
  Created: {self.created_date}
  Last Modified: {self.last_modified}
"""


class StudentManager:
    """
    Manages student records with encrypted file storage
    """
    
    def __init__(self, data_file="students.dat", master_password="SIS_STUDENTS_2024"):
        """
        Initialize student manager
        
        Args:
            data_file (str): File to store encrypted student data
            master_password (str): Master password for encrypting student data
        """
        self.data_file = data_file
        self.master_password = master_password
        self.students = self.load_students()
    
    def load_students(self):
        """
        Load students from encrypted file
        
        Returns:
            dict: Dictionary of students {student_id: Student}
        """
        if not os.path.exists(self.data_file):
            return {}
        
        try:
            with open(self.data_file, 'r') as f:
                encrypted_data = f.read().strip()
            
            if not encrypted_data:
                return {}
            
            # Decrypt student data
            decrypted_data = decrypt_file_data(encrypted_data, self.master_password)
            students_data = json.loads(decrypted_data)
            
            # Convert dictionaries back to Student objects
            students = {}
            for student_id, student_dict in students_data.items():
                students[student_id] = Student.from_dict(student_dict)
            
            return students
            
        except Exception as e:
            print(f"Error loading students: {e}")
            return {}
    
    def save_students(self):
        """
        Save students to encrypted file
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert Student objects to dictionaries
            students_data = {}
            for student_id, student in self.students.items():
                students_data[student_id] = student.to_dict()
            
            # Convert to JSON string
            students_json = json.dumps(students_data, indent=2)
            
            # Encrypt the data
            encrypted_data = encrypt_file_data(students_json, self.master_password)
            
            # Save to file
            with open(self.data_file, 'w') as f:
                f.write(encrypted_data)
            
            return True
            
        except Exception as e:
            print(f"Error saving students: {e}")
            return False
    
    def add_student(self, name, student_id, grade, email="", phone="", address=""):
        """
        Add a new student
        
        Args:
            name (str): Student's name
            student_id (str): Unique student ID
            grade (str): Student's grade
            email (str, optional): Student's email
            phone (str, optional): Student's phone
            address (str, optional): Student's address
            
        Returns:
            tuple: (success: bool, message: str)
        """
        # Validate input
        if not name or not student_id or not grade:
            return False, "Name, Student ID, and Grade are required"
        
        if student_id in self.students:
            return False, f"Student with ID '{student_id}' already exists"
        
        try:
            # Create new student
            student = Student(name, student_id, grade, email, phone, address)
            self.students[student_id] = student
            
            # Save to file
            if self.save_students():
                return True, f"Student '{name}' added successfully"
            else:
                # Remove student if save failed
                del self.students[student_id]
                return False, "Failed to save student data"
                
        except Exception as e:
            return False, f"Failed to add student: {str(e)}"
    
    def get_student(self, student_id):
        """
        Get student by ID
        
        Args:
            student_id (str): Student ID
            
        Returns:
            Student or None: Student object if found, None otherwise
        """
        return self.students.get(student_id)
    
    def search_students(self, query, search_by="name"):
        """
        Search students by name, ID, or grade
        
        Args:
            query (str): Search query
            search_by (str): Field to search by ("name", "id", "grade")
            
        Returns:
            list: List of matching Student objects
        """
        query = query.lower()
        results = []
        
        for student in self.students.values():
            if search_by == "name" and query in student.name.lower():
                results.append(student)
            elif search_by == "id" and query in student.student_id.lower():
                results.append(student)
            elif search_by == "grade" and query in student.grade.lower():
                results.append(student)
        
        return results
    
    def get_all_students(self):
        """
        Get all students
        
        Returns:
            list: List of all Student objects
        """
        return list(self.students.values())
    
    def update_student(self, student_id, **kwargs):
        """
        Update student information
        
        Args:
            student_id (str): Student ID
            **kwargs: Fields to update
            
        Returns:
            tuple: (success: bool, message: str)
        """
        if student_id not in self.students:
            return False, f"Student with ID '{student_id}' not found"
        
        try:
            self.students[student_id].update(**kwargs)
            
            if self.save_students():
                return True, "Student updated successfully"
            else:
                return False, "Failed to save student updates"
                
        except Exception as e:
            return False, f"Failed to update student: {str(e)}"
    
    def delete_student(self, student_id):
        """
        Delete a student
        
        Args:
            student_id (str): Student ID
            
        Returns:
            tuple: (success: bool, message: str)
        """
        if student_id not in self.students:
            return False, f"Student with ID '{student_id}' not found"
        
        try:
            student_name = self.students[student_id].name
            del self.students[student_id]
            
            if self.save_students():
                return True, f"Student '{student_name}' deleted successfully"
            else:
                return False, "Failed to save changes"
                
        except Exception as e:
            return False, f"Failed to delete student: {str(e)}"
    
    def get_statistics(self):
        """
        Get student statistics
        
        Returns:
            dict: Statistics about students
        """
        total_students = len(self.students)
        grades = {}
        
        for student in self.students.values():
            grade = student.grade
            grades[grade] = grades.get(grade, 0) + 1
        
        return {
            'total_students': total_students,
            'grades_distribution': grades
        }


# Test function for student management system
def test_student_system():
    """
    Test function to verify student management system works correctly
    """
    print("Testing Student Management System...")
    
    # Create test student manager
    manager = StudentManager("test_students.dat")
    
    # Test adding student
    success, msg = manager.add_student("John Doe", "S001", "Grade 10", "<EMAIL>")
    print(f"Add student test: {success} - {msg}")
    
    # Test getting student
    student = manager.get_student("S001")
    print(f"Get student test: {student is not None}")
    if student:
        print(student)
    
    # Test search
    results = manager.search_students("john", "name")
    print(f"Search test: Found {len(results)} students")
    
    # Clean up test file
    if os.path.exists("test_students.dat"):
        os.remove("test_students.dat")


if __name__ == "__main__":
    test_student_system()
